name: CI/CD Pipeline

on:
  pull_request:
    branches: [stage]
    types: [opened, synchronize, reopened]
  push:
    branches: [main]
  workflow_dispatch:

env:
  PYTHON_VERSION: "3.12"
  POETRY_VERSION: "2.1.3"
  ECR_REPOSITORY: media-convert
  AWS_REGION: us-east-1

jobs:
  lint:
    name: Code Linting
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Poetry dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pypoetry
            .venv
          key: poetry-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            poetry-${{ runner.os }}-${{ env.PYTHON_VERSION }}-

      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.create true
          poetry config virtualenvs.in-project true

      - name: Install dependencies
        run: poetry install

      - name: Run Ruff linting
        run: make lint

      - name: Run Ruff formatting check
        run: make format-check

  test:
    name: Run Tests
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    needs: lint
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.PYTHON_VERSION }}

      - name: Cache Poetry dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/pypoetry
            .venv
          key: poetry-${{ runner.os }}-${{ env.PYTHON_VERSION }}-${{ hashFiles('**/poetry.lock') }}
          restore-keys: |
            poetry-${{ runner.os }}-${{ env.PYTHON_VERSION }}-

      - name: Install Poetry
        run: |
          curl -sSL https://install.python-poetry.org | python3 -
          echo "$HOME/.local/bin" >> $GITHUB_PATH

      - name: Configure Poetry
        run: |
          poetry config virtualenvs.create true
          poetry config virtualenvs.in-project true

      - name: Install dependencies
        run: poetry install

      - name: Install FFmpeg
        run: |
          sudo apt-get update
          sudo apt-get install -y ffmpeg

      - name: Generate SonarQube reports
        run: make sonar-reports

      - name: Upload test reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-reports
          path: .reports/
          retention-days: 5

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v4
        if: always()
        with:
          file: .reports/coverage.xml
          flags: unittests
          name: codecov-umbrella
          fail_ci_if_error: false

  sonar:
    name: SonarQube Analysis
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' || github.ref == 'refs/heads/main'
    needs: test

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Download test reports
        uses: actions/download-artifact@v4
        with:
          name: test-reports
          path: .reports/

      - name: SonarQube Scan
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: SonarQube Quality Gate Check
        uses: sonarsource/sonarqube-quality-gate-action@master
        timeout-minutes: 5
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

  docker_build_push:
    name: Build and Push Docker Image
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [lint, test]
    outputs:
      image_tag: ${{ steps.meta.outputs.tags }}
      image_digest: ${{ steps.build.outputs.digest }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2

      - name: Extract metadata
        id: meta
        uses: docker/metadata-action@v5
        with:
          images: ${{ steps.login-ecr.outputs.registry }}/${{ env.ECR_REPOSITORY }}
          tags: |
            type=ref,event=branch
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Cache Docker layers
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push Docker image
        id: build
        uses: docker/build-push-action@v5
        with:
          context: .
          file: ./docker/Dockerfile
          push: true
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          platforms: linux/amd64

      - name: Move cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache

  deploy_staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    if: github.event_name == 'pull_request' && github.base_ref == 'stage'
    needs: [lint, test, sonar]
    environment: staging

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name ${{ secrets.EKS_CLUSTER_STAGING }}

      - name: Deploy to staging
        run: |
          kubectl rollout restart deployment/${{ secrets.APP_NAME_STAGING }} -n ${{ secrets.NAMESPACE_STAGING || 'default' }}
          kubectl rollout status deployment/${{ secrets.APP_NAME_STAGING }} -n ${{ secrets.NAMESPACE_STAGING || 'default' }} --timeout=300s

      - name: Verify deployment
        run: |
          kubectl get pods -l app=${{ secrets.APP_NAME_STAGING }} -n ${{ secrets.NAMESPACE_STAGING || 'default' }}

  deploy_production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    needs: [docker_build_push]
    environment: production

    steps:
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}

      - name: Update kubeconfig
        run: |
          aws eks update-kubeconfig --region ${{ env.AWS_REGION }} --name ${{ secrets.EKS_CLUSTER_PRODUCTION }}

      - name: Update deployment image
        run: |
          kubectl set image deployment/${{ secrets.APP_NAME_PRODUCTION }} \
            ${{ secrets.APP_NAME_PRODUCTION }}=${{ needs.docker_build_push.outputs.image_tag }} \
            -n ${{ secrets.NAMESPACE_PRODUCTION || 'default' }}

      - name: Wait for rollout
        run: |
          kubectl rollout status deployment/${{ secrets.APP_NAME_PRODUCTION }} -n ${{ secrets.NAMESPACE_PRODUCTION || 'default' }} --timeout=600s

      - name: Verify deployment
        run: |
          kubectl get pods -l app=${{ secrets.APP_NAME_PRODUCTION }} -n ${{ secrets.NAMESPACE_PRODUCTION || 'default' }}
          kubectl describe deployment/${{ secrets.APP_NAME_PRODUCTION }} -n ${{ secrets.NAMESPACE_PRODUCTION || 'default' }}

  notify_success:
    name: Notify Success
    runs-on: ubuntu-latest
    if: always() && (needs.deploy_production.result == 'success' || needs.deploy_staging.result == 'success')
    needs: [deploy_staging, deploy_production]

    steps:
      - name: Notify deployment success
        run: |
          if [[ "${{ needs.deploy_production.result }}" == "success" ]]; then
            echo "✅ Production deployment successful!"
          elif [[ "${{ needs.deploy_staging.result }}" == "success" ]]; then
            echo "✅ Staging deployment successful!"
          fi
